# Примеры использования BLE Terminal

## Пример запуска

```bash
$ cargo run
BLE Terminal - поиск устройств...
Сканирование в течение 10 секунд...

Найденные устройства:
1. ESP32-BLE-Device (aa:bb:cc:dd:ee:ff)
2. <PERSON><PERSON>uin<PERSON> 33 BLE (11:22:33:44:55:66)
3. Неизвестное устройство (99:88:77:66:55:44)

Выберите устройство (введите номер): 1
Подключение к устройству: ESP32-BLE-Device
Подключено!
Найден сервис: 000000f3-0000-1000-8000-00805f9b34fb
Найдена характеристика для чтения: 0000f307-0000-1000-8000-00805f9b34fb
Найдена характеристика для записи: 0000f308-0000-1000-8000-00805f9b34fb
Подписка на уведомления активирована

Терминал готов! Введите команды для отправки на устройство:
(Данные от устройства будут отображаться автоматически)
Нажмите Ctrl+C для выхода
----------------------------------------
> hello
Отправлено: hello
I (170974) -queues hlp-: sending uart cmd: == heartbeat ==
> status
Отправлено: status
Device status: OK
Temperature: 23.5C
  ASCII: Temp: 23.5C
  Bytes: [84, 101, 109, 112, 58, 32, 50, 51, 46, 53, 67]
----------------------------------------
```

## Пример вывода различных типов данных

### Текстовые данные
```
Получены данные:
  Hex: 53 65 6e 73 6f 72 20 64 61 74 61
  ASCII: Sensor data
  Bytes: [83, 101, 110, 115, 111, 114, 32, 100, 97, 116, 97]
```

### Бинарные данные
```
Получены данные:
  Hex: ff 00 a5 5a 12 34 56 78
  ASCII: ....V4x
  Bytes: [255, 0, 165, 90, 18, 52, 86, 120]
```

### Смешанные данные
```
Получены данные:
  Hex: 44 61 74 61 3a 20 ff 00 a5 0a
  ASCII: Data: ....
  Bytes: [68, 97, 116, 97, 58, 32, 255, 0, 165, 10]
```

## Настройка для других UUID

Если вам нужно использовать другие UUID сервиса и характеристик, измените константы в `src/main.rs`:

```rust
// Замените эти UUID на нужные вам
const SERVICE_UUID: Uuid = Uuid::from_u128(0x000000F3_0000_1000_8000_00805f9b34fb);
const READ_CHARACTERISTIC_UUID: Uuid = Uuid::from_u128(0x0000F307_0000_1000_8000_00805f9b34fb);  // Для чтения данных от устройства
const WRITE_CHARACTERISTIC_UUID: Uuid = Uuid::from_u128(0x0000F308_0000_1000_8000_00805f9b34fb); // Для отправки команд на устройство
```

## Возможные ошибки и их решения

### "Не найдено BLE адаптеров"
```
BLE Terminal - поиск устройств...
Не найдено BLE адаптеров
```
**Решение**: Убедитесь, что BLE адаптер включен в системе.

### "Устройства не найдены"
```
BLE Terminal - поиск устройств...
Сканирование в течение 10 секунд...
Устройства не найдены
```
**Решение**: Убедитесь, что BLE устройства находятся поблизости и в режиме обнаружения.

### "Сервис не найден"
```
Подключение к устройству: MyDevice
Подключено!
Error: "Сервис не найден"
```
**Решение**: Проверьте правильность UUID сервиса или убедитесь, что устройство поддерживает нужный сервис.

### "Характеристика не найдена"
```
Найден сервис: 000000f3-0000-1000-8000-00805f9b34fb
Error: "Характеристика не найдена"
```
**Решение**: Проверьте правильность UUID характеристики.

## Расширение функциональности

### Интерактивный ввод команд

Приложение поддерживает двунаправленную связь:
- **Чтение данных**: Автоматически получает и отображает данные от устройства с поддержкой ANSI цветов
- **Отправка команд**: Позволяет вводить команды в консоли и отправлять их на устройство

Просто введите команду в консоли и нажмите Enter:
```
> help
Отправлено: help
Available commands: status, reset, config
> status
Отправлено: status
Device status: OK
```

### Добавление фильтрации по имени устройства

Для поиска только определенных устройств:

```rust
let scan_filter = ScanFilter {
    services: vec![SERVICE_UUID],
};
central.start_scan(scan_filter).await?;
```

### Добавление логирования

Добавьте в `Cargo.toml`:
```toml
[dependencies]
env_logger = "0.10"
log = "0.4"
```

И в начало `main()`:
```rust
env_logger::init();
log::info!("Запуск BLE Terminal");
```
