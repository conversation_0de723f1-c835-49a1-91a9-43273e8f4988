{"rustc": 12488743700189009532, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 4544263003301826467, "deps": [[5103565458935487, "futures_io", false, 17991978513377584623], [1811549171721445101, "futures_channel", false, 15815383291562987412], [7013762810557009322, "futures_sink", false, 1194217625767149287], [7620660491849607393, "futures_core", false, 783387196134002097], [10629569228670356391, "futures_util", false, 14597590539872308620], [12779779637805422465, "futures_executor", false, 10451106785004573695], [16240732885093539806, "futures_task", false, 12767956359648008539]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-395e8d4514efd206\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}