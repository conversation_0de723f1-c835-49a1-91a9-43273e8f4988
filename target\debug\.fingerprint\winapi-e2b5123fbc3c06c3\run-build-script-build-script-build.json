{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10020888071089587331, "build_script_build", false, 9731229045348605399]], "local": [{"RerunIfChanged": {"output": "debug\\build\\winapi-e2b5123fbc3c06c3\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "WINAPI_NO_BUNDLED_LIBRARIES", "val": null}}, {"RerunIfEnvChanged": {"var": "WINAPI_STATIC_NOBUNDLE", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}