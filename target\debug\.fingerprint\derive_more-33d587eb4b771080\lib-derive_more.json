{"rustc": 12488743700189009532, "features": "[\"default\", \"is_variant\", \"std\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"std\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 7165309211519594838, "profile": 1613925905003419231, "path": 1633004847690775647, "deps": [[15774985133158646067, "derive_more_impl", false, 903977378227059929]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-33d587eb4b771080\\dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}