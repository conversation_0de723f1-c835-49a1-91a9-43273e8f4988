{"rustc": 12488743700189009532, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 7200522559614294938, "deps": [[5103565458935487, "futures_io", false, 17991978513377584623], [1615478164327904835, "pin_utils", false, 6768658885926745329], [1811549171721445101, "futures_channel", false, 15815383291562987412], [1906322745568073236, "pin_project_lite", false, 6434172772222006624], [5451793922601807560, "slab", false, 106631164748493541], [7013762810557009322, "futures_sink", false, 1194217625767149287], [7620660491849607393, "futures_core", false, 783387196134002097], [10565019901765856648, "futures_macro", false, 7671455108370483838], [15932120279885307830, "memchr", false, 12015112111801992520], [16240732885093539806, "futures_task", false, 12767956359648008539]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-6542e54c63020bce\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}