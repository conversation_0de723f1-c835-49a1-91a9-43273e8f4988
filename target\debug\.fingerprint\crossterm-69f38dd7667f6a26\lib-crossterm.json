{"rustc": 12488743700189009532, "features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"events\", \"windows\"]", "declared_features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"event-stream\", \"events\", \"filedescriptor\", \"libc\", \"osc52\", \"serde\", \"use-dev-tty\", \"windows\"]", "target": 7162149947039624270, "profile": 15657897354478470176, "path": 11186238673004890830, "deps": [[4495526598637097934, "parking_lot", false, 17689941715618120102], [7896293946984509699, "bitflags", false, 15595928608632754491], [10020888071089587331, "<PERSON>ap<PERSON>", false, 11267712909789661783], [11293676373856528358, "derive_more", false, 15810876383995820552], [11763018104473073732, "document_features", false, 6967180001657141558], [17658759660230624279, "crossterm_winapi", false, 12732394148938951923]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\crossterm-69f38dd7667f6a26\\dep-lib-crossterm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}