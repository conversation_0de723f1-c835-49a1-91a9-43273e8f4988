{"rustc": 12488743700189009532, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 7200522559614294938, "deps": [[5103565458935487, "futures_io", false, 11531965683994299525], [1615478164327904835, "pin_utils", false, 9765220959257522058], [1811549171721445101, "futures_channel", false, 259580821119722580], [1906322745568073236, "pin_project_lite", false, 4110872939955711948], [5451793922601807560, "slab", false, 16466117533961851334], [7013762810557009322, "futures_sink", false, 16011669210422267663], [7620660491849607393, "futures_core", false, 18075119827378030879], [10565019901765856648, "futures_macro", false, 7671455108370483838], [15932120279885307830, "memchr", false, 302141038584812133], [16240732885093539806, "futures_task", false, 12187081770721220552]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-ad23790f9ce533ed\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}