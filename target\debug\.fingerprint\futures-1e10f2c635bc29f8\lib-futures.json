{"rustc": 12488743700189009532, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 17467636112133979524, "path": 4544263003301826467, "deps": [[5103565458935487, "futures_io", false, 11531965683994299525], [1811549171721445101, "futures_channel", false, 259580821119722580], [7013762810557009322, "futures_sink", false, 16011669210422267663], [7620660491849607393, "futures_core", false, 18075119827378030879], [10629569228670356391, "futures_util", false, 14762351629978799950], [12779779637805422465, "futures_executor", false, 4055497036547445410], [16240732885093539806, "futures_task", false, 12187081770721220552]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-1e10f2c635bc29f8\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}