// Тестовый модуль для проверки парсинга ANSI кодов
use crate::{print_colored_string, parse_ansi_color};
use colored::Color;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_ansi_color() {
        // Тест зеленого цвета
        assert_eq!(parse_ansi_color("\x1B[32m"), Some(Color::Green));

        // Тест красного цвета
        assert_eq!(parse_ansi_color("\x1B[31m"), Some(Color::Red));

        // Тест сброса цвета
        assert_eq!(parse_ansi_color("\x1B[0m"), None);

        // Тест комбинированного кода
        assert_eq!(parse_ansi_color("\x1B[0;32m"), Some(Color::Green));

        // Тест яркого синего
        assert_eq!(parse_ansi_color("\x1B[94m"), Some(Color::BrightBlue));
    }

    #[test]
    fn test_print_colored_string() {
        // Тестовые данные как в примере
        let test_data = b".\x1B[0;32mI (170974) -queues hlp-: sending uart cmd: == heartbeat ==\x1B[0m.";

        println!("Тест цветного вывода:");
        print_colored_string(test_data);

        // Тест с разными цветами
        let test_data2 = b"\x1B[31mError:\x1B[0m \x1B[33mWarning message\x1B[0m \x1B[32mSuccess!\x1B[0m";
        print_colored_string(test_data2);
    }
}