use btleplug::api::{<PERSON>, Manager as _, Peripheral as _, <PERSON>an<PERSON><PERSON>er, WriteType};
use btleplug::platform::Manager;
use colored::*;
use futures::stream::StreamExt;
use regex::Regex;
use std::error::Error;
use std::io::{self, Write};
use std::time::Duration;
use tokio::time;
use tokio::io::{AsyncReadExt};
use uuid::Uuid;

#[cfg(test)]
mod test_ansi;

// UUID сервиса и характеристик
const SERVICE_UUID: Uuid = Uuid::from_u128(0x000000F3_0000_1000_8000_00805f9b34fb);
const READ_CHARACTERISTIC_UUID: Uuid = Uuid::from_u128(0x0000F307_0000_1000_8000_00805f9b34fb);
const WRITE_CHARACTERISTIC_UUID: Uuid = Uuid::from_u128(0x0000F308_0000_1000_8000_00805f9b34fb);

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    println!("BLE Terminal - поиск устройств...");

    let manager = Manager::new().await?;
    let adapters = manager.adapters().await?;

    if adapters.is_empty() {
        eprintln!("Не найдено BLE адаптеров");
        return Ok(());
    }

    let central = &adapters[0];

    central.start_scan(ScanFilter::default()).await?;

    println!("Сканирование в течение 10 секунд...");
    time::sleep(Duration::from_secs(10)).await;

    central.stop_scan().await?;

    let peripherals = central.peripherals().await?;

    if peripherals.is_empty() {
        println!("Устройства не найдены");
        return Ok(());
    }

    println!("\nНайденные устройства:");
    for (i, peripheral) in peripherals.iter().enumerate() {
        let properties = peripheral.properties().await?;
        let name = properties
            .unwrap_or_default()
            .local_name
            .unwrap_or_else(|| "Неизвестное устройство".to_string());
        println!("{}. {} ({})", i + 1, name, peripheral.address());
    }

    print!("\nВыберите устройство (введите номер): ");
    io::stdout().flush()?;

    let mut input = String::new();
    io::stdin().read_line(&mut input)?;

    let choice: usize = input.trim().parse().unwrap_or(0);

    if choice == 0 || choice > peripherals.len() {
        println!("Неверный выбор");
        return Ok(());
    }

    let peripheral = &peripherals[choice - 1];
    let properties = peripheral.properties().await?;
    let device_name = properties
        .unwrap_or_default()
        .local_name
        .unwrap_or_else(|| "Неизвестное устройство".to_string());

    println!("Подключение к устройству: {}", device_name);

    peripheral.connect().await?;
    println!("Подключено!");

    peripheral.discover_services().await?;

    let services = peripheral.services();
    let service = services
        .iter()
        .find(|s| s.uuid == SERVICE_UUID)
        .ok_or("Сервис не найден")?;

    println!("Найден сервис: {}", service.uuid);

    // Найдем характеристику для чтения (уведомления)
    let read_characteristic = service
        .characteristics
        .iter()
        .find(|c| c.uuid == READ_CHARACTERISTIC_UUID)
        .ok_or("Характеристика для чтения не найдена")?;

    println!("Найдена характеристика для чтения: {}", read_characteristic.uuid);

    // Найдем характеристику для записи
    let write_characteristic = service
        .characteristics
        .iter()
        .find(|c| c.uuid == WRITE_CHARACTERISTIC_UUID)
        .ok_or("Характеристика для записи не найдена")?;

    println!("Найдена характеристика для записи: {}", write_characteristic.uuid);

    peripheral.subscribe(read_characteristic).await?;
    println!("Подписка на уведомления активирована");

    println!("\nТерминал готов! Введите команды для отправки на устройство:");
    println!("(Данные от устройства будут отображаться автоматически)");
    println!("Нажмите Ctrl+C для выхода");
    println!("----------------------------------------");

    // Создаем поток для посимвольного чтения пользовательского ввода
    let mut stdin = tokio::io::stdin();
    let mut input_buffer = [0u8; 1];
    let mut current_input = String::new();

    // Получаем поток уведомлений
    let mut notification_stream = peripheral.notifications().await?;

    // Показываем приглашение для ввода
    print!("> ");
    io::stdout().flush()?;

    loop {
        tokio::select! {
            // Обработка уведомлений от устройства
            notification = notification_stream.next() => {
                if let Some(data) = notification {
                    if data.uuid == READ_CHARACTERISTIC_UUID {
                        // Если есть частично набранный текст, сохраняем его
                        let has_input = !current_input.is_empty();

                        // Очищаем текущую строку
                        print!("\r\x1B[K");

                        // Выводим данные от устройства
                        print_colored_string(&data.value);

                        // Принудительно сбрасываем буфер после вывода данных устройства
                        io::stdout().flush()?;

                        // Восстанавливаем строку ввода с текущим содержимым
                        print!("> {}", current_input);

                        // Если был частично набранный текст, добавляем отладочную информацию
                        if has_input {
                            // Добавляем визуальный индикатор восстановления (временно для отладки)
                            print!(" [восстановлено: '{}']", current_input);
                        }

                        io::stdout().flush()?;
                    }
                }
            }
            // Посимвольное чтение пользовательского ввода
            input_result = stdin.read(&mut input_buffer) => {
                match input_result {
                    Ok(0) => break, // EOF
                    Ok(_) => {
                        let byte = input_buffer[0];

                        match byte {
                            // Enter (LF или CR)
                            b'\n' | b'\r' => {
                                if !current_input.is_empty() {
                                    println!(); // Переход на новую строку

                                    // Отправляем команду на устройство
                                    match peripheral.write(write_characteristic, current_input.as_bytes(), WriteType::WithoutResponse).await {
                                        Ok(_) => {
                                            println!("Отправлено: {}", current_input);
                                        }
                                        Err(e) => {
                                            eprintln!("Ошибка отправки: {}", e);
                                        }
                                    }

                                    current_input.clear();
                                } else {
                                    println!(); // Просто переход на новую строку для пустого ввода
                                }

                                print!("> ");
                                io::stdout().flush()?;
                            }

                            // Backspace (BS или DEL)
                            b'\x08' | b'\x7f' => {
                                if !current_input.is_empty() {
                                    current_input.pop();
                                    // Перерисовываем всю строку ввода для корректного отображения
                                    print!("\r\x1B[K> {}", current_input);
                                    io::stdout().flush()?;
                                }
                            }

                            // Ctrl+C
                            b'\x03' => {
                                println!("\nВыход...");
                                break;
                            }

                            // Игнорируем другие управляющие символы
                            b if b < 32 => {
                                // Игнорируем управляющие символы (кроме обработанных выше)
                            }

                            // Обычные печатные символы
                            b => {
                                if let Ok(ch) = std::str::from_utf8(&[b]) {
                                    // Добавляем символ к текущему вводу
                                    current_input.push_str(ch);

                                    // Для надежности перерисовываем всю строку ввода
                                    // Это гарантирует корректное отображение при многобайтовых символах
                                    print!("\r\x1B[K> {}", current_input);
                                    io::stdout().flush()?;
                                }
                            }
                        }
                    }
                    Err(e) => {
                        eprintln!("Ошибка чтения ввода: {}", e);
                        break;
                    }
                }
            }
        }
    }

    Ok(())
}

// Функция для преобразования байтов в hex строку
fn hex_string(bytes: &[u8]) -> String {
    bytes
        .iter()
        .map(|b| format!("{:02x}", b))
        .collect::<Vec<_>>()
        .join(" ")
}



// Функция для обработки и вывода цветного текста с ANSI кодами
pub fn print_colored_string(bytes: &[u8]) {
    let text = String::from_utf8_lossy(bytes);

    // Регулярное выражение для поиска ANSI escape последовательностей
    let ansi_regex = Regex::new(r"\x1B\[([0-9;]*)m").unwrap();

    let mut last_end = 0;
    let mut current_color: Option<Color> = None;

    for mat in ansi_regex.find_iter(&text) {
        // Выводим текст до ANSI кода
        let before_ansi = &text[last_end..mat.start()];
        if !before_ansi.is_empty() {
            if let Some(color) = current_color {
                print!("{}", before_ansi.color(color));
            } else {
                print!("{}", before_ansi);
            }
        }

        // Парсим ANSI код
        let ansi_code = &text[mat.start()..mat.end()];
        current_color = parse_ansi_color(ansi_code);

        last_end = mat.end();
    }

    // Выводим оставшийся текст
    let remaining = &text[last_end..];
    if !remaining.is_empty() {
        if let Some(color) = current_color {
            print!("{}", remaining.color(color));
        } else {
            print!("{}", remaining);
        }
    }

    // Добавляем перенос строки, если его нет
    if !text.ends_with('\n') {
        print!("\n");
    }
}

// Функция для парсинга ANSI цветных кодов
pub fn parse_ansi_color(ansi_code: &str) -> Option<Color> {
    // Извлекаем числовые коды из ANSI последовательности
    let codes_str = ansi_code.trim_start_matches("\x1B[").trim_end_matches('m');

    if codes_str.is_empty() || codes_str == "0" {
        return None; // Сброс цвета
    }

    // Парсим коды, разделенные точкой с запятой
    let codes: Vec<u8> = codes_str
        .split(';')
        .filter_map(|s| s.parse().ok())
        .collect();

    // Ищем цветовые коды в последовательности
    for &code in &codes {
        match code {
            // Стандартные цвета (30-37)
            30 => return Some(Color::Black),
            31 => return Some(Color::Red),
            32 => return Some(Color::Green),
            33 => return Some(Color::Yellow),
            34 => return Some(Color::Blue),
            35 => return Some(Color::Magenta),
            36 => return Some(Color::Cyan),
            37 => return Some(Color::White),

            // Яркие цвета (90-97)
            90 => return Some(Color::BrightBlack),
            91 => return Some(Color::BrightRed),
            92 => return Some(Color::BrightGreen),
            93 => return Some(Color::BrightYellow),
            94 => return Some(Color::BrightBlue),
            95 => return Some(Color::BrightMagenta),
            96 => return Some(Color::BrightCyan),
            97 => return Some(Color::BrightWhite),

            // Игнорируем другие коды (жирный, курсив и т.д.)
            _ => continue,
        }
    }

    None
}
